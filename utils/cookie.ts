import Cookies from 'js-cookie'

export const cookieUtil = {
  // 设置cookie
  setCookie: (name: string, value: string, options?: Cookies.CookieAttributes) => {
    Cookies.set(name, value, {
      domain: window.location.hostname,
      ...options
    })
  },

  // 获取cookie
  getCookie: (name: string) => {
    return Cookies.get(name)
  },

  // 删除cookie
  removeCookie: (name: string, options?: Cookies.CookieAttributes) => {
    Cookies.remove(name, {
      domain: window.location.hostname,
      ...options
    })
  }
} 