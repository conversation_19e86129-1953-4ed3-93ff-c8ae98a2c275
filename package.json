{"name": "postgres-nuxt", "repository": "https://github.com/vercel/examples.git", "license": "MIT", "version": "0.0.0", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/content": "^2.13.4", "@nuxtjs/i18n": "^8.0.0", "@nuxtjs/sitemap": "^6.0.0-beta.2", "@types/js-cookie": "^3.0.6", "@types/node": "^18.19.68", "@types/node-fetch": "^2.6.12", "@unlok-co/nuxt-stripe": "^3.0.0", "autoprefixer": "^10.4.20", "nuxt": "^3.12.4", "openai": "^4.1.0", "postcss": "^8.4.41", "postcss-px-to-viewport": "^1.1.1", "tailwindcss": "^3.4.10", "turbo": "^1.9.3"}, "dependencies": {"@supabase/supabase-js": "^2.45.1", "@tailwindcss/typography": "^0.5.16", "@vercel/analytics": "^1.3.1", "@vercel/postgres": "^0.1.3", "@vueuse/core": "^11.3.0", "better-sqlite3": "^12.2.0", "google-auth-library": "^9.14.0", "js-cookie": "^3.0.5", "lucide-vue-next": "^0.469.0", "ms": "^2.1.3", "node-fetch": "^2.7.0", "nuxt-security": "^2.0.0-rc.9", "radix-vue": "^1.9.11", "stripe": "^16.8.0", "tailwindcss-animate": "^1.0.7", "ws": "^8.13.0"}}