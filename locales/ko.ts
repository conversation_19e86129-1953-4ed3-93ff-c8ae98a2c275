export default {
  $locale: {
    name: 'Korean',
    nativeName: '한국어'
  },
  welcome: 'Fillify에 오신 것을 환영합니다',
  description: 'AI 기반 자동 양식 작성',
  nav: {
    home: '홈',
    blog: '블로그',
    signin: '로그인',
    dashboard: '대시보드',
    signout: '로그아웃',
    startFree: '무료로 시작하기',
    language: '언어'
  },
  hero: {
    chromeStore: 'Chrome 웹 스토어에서 만나보세요',
    title: {
      text: 'AI로 완전히 새로워진 양식 작성',
      rotatingWords: {
        0: 'AI의 마법 같은 기술',
        1: '스마트 자동화',
        2: '미래형 테크놀로지',
        3: '완벽한 정확도',
        4: '끊김 없는 통합'
      }
    },
    description: '한 문장만 입력하면 AI가 웹 양식을 즉시 채워줍니다. 온라인 양식을 다루는 가장 스마트한 방법입니다.',
    cta: {
      chrome: 'Chrome에 추가',
      learnMore: '자세히 알아보기'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: '일일 자동 작성된 양식'
    },
    accuracy: {
      value: '%',
      label: '정확도'
    },
    support: {
      value: '24/7',
      label: 'AI 실시간 지원'
    }
  },
  features: {
    title: 'AI의 혁신적인 기능을 경험하세요',
    subtitle: 'Fillify가 자동화를 통해 어떻게 업무 효율을 극대화하는지 확인해 보세요',
    formFilling: {
      title: '범용 양식 작성',
      description: 'AI의 정확성으로 모든 웹 양식을 작성하세요. 간단한 등록 양식부터 복잡한 신청서까지, Fillify는 귀하의 텍스트 설명을 정확한 양식 데이터로 변환하여 수작업 시간을 절약해 드립니다.',
      alt: 'Fillify가 AI를 사용하여 웹 양식을 자동으로 작성하는 화면 스크린샷'
    },
    email: {
      title: '스마트 이메일 어시스턴트',
      description: '우리의 AI 어시스턴트로 전문적인 이메일을 즉시 작성하세요. Gmail과 Outlook을 지원하며, 귀하의 간단한 설명을 잘 구성된 이메일로 변환하여 이메일 작성을 쉽게 만듭니다.',
      alt: 'Fillify가 AI 제안을 사용하여 이메일을 작성하는 데모'
    },
    bugReport: {
      title: '지능형 버그 리포트',
      description: '원클릭으로 포괄적인 버그 리포트를 생성하세요. GitHub Issues, JIRA 등의 플랫폼을 지원하며, 우리의 AI가 귀하의 간단한 설명을 상세하고 구조화된 리포트로 변환하여 팀 커뮤니케이션을 효과적으로 돕습니다.',
      alt: 'Fillify가 GitHub Issues용 상세 버그 리포트를 생성하는 예시'
    },
    aiProvider: {
      title: '원하는 AI 제공업체 선택',
      description: 'OpenAI, Anthropic Claude, Google Gemini, Moonshot AI 등 다양한 AI 서비스를 자유롭게 선택할 수 있습니다. API 키만 입력하면 원하는 AI 모델을 바로 사용할 수 있으며, 간편하게 제공업체를 변경할 수도 있습니다.',
      alt: 'Fillify에서 다양한 AI 제공업체를 선택할 수 있는 인터페이스'
    }
  },
  faq: {
    title: '자주 묻는 질문',
    items: {
      what: {
        question: 'Fillify는 어떤 서비스인가요?',
        answer: 'Fillify는 한 문장만 입력하면 웹 양식을 즉시 채워주는 AI 기반 Chrome 확장 프로그램입니다. 가입 양식, 버그 리포트, 이메일 작성 등 다양한 양식을 자동으로 작성할 수 있습니다.'
      },
      types: {
        question: 'Fillify는 어떤 양식을 지원하나요?',
        answer: '일반 웹 양식, 버그 리포트, 이메일 등 다양한 유형의 양식을 지원합니다. 텍스트 필드, 텍스트 영역 등을 자동으로 채워 원활한 자동화를 제공합니다.'
      },
      providers: {
        question: 'Fillify는 어떤 AI 제공업체와 연동되나요?',
        answer: 'Fillify는 OpenAI, Anthropic Claude, Google Gemini, Moonshot AI를 포함한 다양한 AI 제공업체와 연동됩니다. 사용자는 원하는 제공업체를 선택하고 API 키를 연결하여 자유롭게 활용할 수 있습니다.'
      },
      privacy: {
        question: 'Fillify는 개인정보를 어떻게 보호하나요?',
        answer: "저희는 고객님의 개인정보 및 데이터 보안을 매우 중요하게 생각합니다. API 키는 브라우저에 로컬로 저장되며, 요청 시에만 백엔드 서버로 전송되어 암호화된 후 선택하신 AI 서비스 제공업체로 전달됩니다. 저희는 API 키를 저장하거나 악용하지 않으며, 제3자와 공유하지 않습니다. 안심하고 사용하세요."
      },
      customize: {
        question: '특정 양식에 맞게 AI 응답을 설정할 수 있나요?',
        answer: '네! 버그 리포트 모드에서는 미리 정의된 정보를 포함한 맞춤 템플릿을 설정하여 더욱 정확하고 일관된 리포트를 생성할 수 있습니다.'
      },
      languages: {
        question: 'Fillify는 어떤 언어를 지원하나요?',
        answer: 'Fillify는 여러 언어를 지원하며, 양식의 언어를 자동으로 감지합니다. 또한 확장 프로그램에서 원하는 출력 언어를 직접 선택할 수도 있습니다.'
      }
    }
  },
  bottomCta: {
    subtitle: '업무 효율을 극대화할 준비가 되셨나요?',
    title: '지금 바로 AI 기반 자동 양식 작성의 혁신을 경험해 보세요',
    button: '지금 설치하기'
  },
  footer: {
    copyright: '© {year} Fillify. 모든 권리 보유.',
    social: {
      twitter: 'X (트위터)',
      youtube: '유튜브'
    },
    links: {
      terms: '이용 약관',
      privacy: '개인정보 보호정책'
    }
  },
  signin: {
    title: 'Fillify에 오신 것을 환영합니다',
    subtitle: 'AI 기반 자동 양식 작성 기능을 사용하려면 로그인하세요',
    email: {
      label: '이메일 주소',
      placeholder: '이메일 주소를 입력하세요',
      continue: '이메일로 계속하기',
      sending: '전송 중...'
    },
    otp: {
      sentMessage: '다음 주소로 인증 코드를 보냈습니다',
      label: '로그인 코드',
      placeholder: '인증 코드를 입력하세요',
      continue: '로그인 코드로 계속하기',
      verifying: '인증 중...',
      resend: {
        resending: '재전송 중...',
        cooldown: '{seconds}초 후 재전송',
        action: '인증 코드 재전송'
      }
    },
    divider: '또는',
    error: {
      invalidEmail: '유효한 이메일 주소를 입력하세요',
      sendOTP: '인증 코드 전송에 실패했습니다. 다시 시도해 주세요.',
      invalidOTP: '유효한 6자리 인증 코드를 입력하세요',
      verifyOTP: '인증 코드가 유효하지 않거나 만료되었습니다. 다시 시도해 주세요.',
      googleLogin: 'Google 로그인에 실패했습니다. 다시 시도해 주세요.'
    },
    features: {
      title: '로그인하면 제공되는 기능:',
      list: {
        autoFill: 'AI 기반 자동 양식 작성',
        api: '맞춤 API 연결',
        early: '새로운 기능 사전 체험'
      }
    },
    terms: {
      prefix: '로그인하면 당사의',
      and: '및',
      termsOfService: '이용 약관',
      privacyPolicy: '개인정보 보호정책'
    },
    seo: {
      title: '로그인 - Fillify',
      description: 'Fillify에 로그인하여 AI 기반 자동 양식 작성 기능을 이용하세요'
    }
  },
  meta: {
    title: 'Fillify - 양식, 이메일, 버그 리포트를 위한 AI 어시스턴트',
    description: 'Fillify는 AI 기술로 양식 작성을 혁신합니다. 웹 양식 자동 완성, 이메일 작성, 버그 리포트 생성을 지능형 자동화로 구현합니다.',
    keywords: {
      formFilling: 'AI 양식 작성',
      automation: 'AI 자동화',
      email: 'AI 이메일 생성',
      bugReport: 'AI 버그 리포트 생성',
      additional: [
        '스마트 양식 작성',
        '자동 데이터 입력',
        'AI 양식 도우미',
        '지능형 양식 작성',
        'Chrome 양식 자동 완성',
        'AI 양식 자동 작성기'
      ]
    }
  },
  privacy: {
    meta: {
      title: '개인정보 보호정책 - Fillify',
      description: 'Fillify가 개인정보를 보호하고 데이터를 처리하는 방식에 대해 알아보세요.'
    },
    title: '개인정보 보호정책',
    lastUpdated: '최종 업데이트: {date}'
  },
  terms: {
    meta: {
      title: '이용 약관 - Fillify',
      description: 'Fillify 서비스 이용 약관을 확인하세요.'
    },
    title: '이용 약관',
    lastUpdated: '최종 업데이트: {date}'
  },
  dashboard: {
    meta: {
      title: '대시보드 - Fillify',
      description: 'Fillify 계정을 관리하고 현재 플랜을 확인하며 사용량을 추적하세요.'
    },
    currentPlan: '현재 플랜',
    settings: '설정',
    usageOverview: '사용량 개요',
    creditsUsed: '사용한 크레딧'
  },
  blog: {
    meta: {
      title: '블로그 - Fillify',
      description: 'AI 기반 양식 작성과 생산성 자동화에 대한 최신 뉴스, 업데이트, 팁을 읽어보세요.'
    },
    hero: {
      badge: '최신 업데이트',
      title: '블로그',
      subtitle: '최신 뉴스, 릴리스, 팁'
    },
    list: {
      readMore: '더 읽기',
      publishedOn: '게시일',
      minRead: '분 소요',
      noPostsTitle: '아직 게시물이 없습니다',
      noPostsDescription: '훌륭한 콘텐츠를 준비 중입니다. 잠시 후 다시 확인해 주세요.'
    },
    article: {
      backToBlog: '블로그로 돌아가기',
      thanksTitle: '읽어주셔서 감사합니다!',
      thanksDescription: 'Fillify에 대한 질문이나 제안이 있으시면 언제든지 연락해 주세요.',
      tryFillify: 'Fillify 사용해보기',
      moreArticles: '더 많은 글',
      notFoundTitle: '글을 찾을 수 없습니다',
      notFoundDescription: '죄송합니다. 찾으시는 글이 존재하지 않거나 삭제되었습니다.',
      backToBlogBtn: '블로그로 돌아가기'
    }
  },
  '404': {
    title: '페이지를 찾을 수 없습니다',
    description: '죄송합니다. 요청하신 페이지를 찾을 수 없습니다. URL을 확인하시거나 홈페이지로 돌아가주세요.',
    backHome: '홈으로 돌아가기'
  }
}