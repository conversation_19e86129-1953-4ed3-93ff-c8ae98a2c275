export default {
  $locale: {
    name: 'Japanese',
    nativeName: '日本語'
  },
  welcome: 'Fillifyへようこそ',
  description: 'AIによるフォーム自動入力',
  nav: {
    home: 'ホーム',
    blog: 'ブログ',
    signin: 'サインイン',
    dashboard: 'ダッシュボード',
    signout: 'サインアウト',
    startFree: '無料で始める',
    language: '言語'
  },
  hero: {
    chromeStore: 'Chrome ウェブストアで提供中',
    title: {
      text: 'AIでフォーム入力を革新 ',
      rotatingWords: {
        0: 'AIの魔法',
        1: 'スマートオートメーション',
        2: '次世代テクノロジー',
        3: '完璧な精度',
        4: 'シームレスな統合'
      }
    },
    description: '一文入力するだけで、AIが瞬時にあらゆるウェブフォームを自動記入。オンラインフォームを最もスマートに処理する方法です。',
    cta: {
      chrome: 'Chrome に追加',
      learnMore: '詳しく見る'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: '毎日記入されるフォーム数'
    },
    accuracy: {
      value: '%',
      label: '精度'
    },
    support: {
      value: '24/7',
      label: 'AIサポート'
    }
  },
  features: {
    title: 'AIの力を体験',
    subtitle: 'Fillifyのインテリジェントな自動化が、あなたのワークフローをどのように変えるのかをご紹介',
    formFilling: {
      title: 'ユニバーサルフォーム入力',
      description: 'AIの精度で任意のウェブフォームを入力。簡単な登録フォームから複雑な申請書まで、Fillifyはあなたのテキスト説明を正確なフォームデータに変換し、手作業の時間を大幅に節約します。',
      alt: 'FillifyがAIを使用してウェブフォームを自動入力する画面のスクリーンショット'
    },
    email: {
      title: 'スマートメールアシスタント',
      description: '私たちのAIアシスタントで瞬時にプロフェッショナルなメールを作成。GmailとOutlookに対応し、あなたの簡単な説明を整理された構造的なメールに変換し、メール作成を簡単にします。',
      alt: 'FillifyがAIの提案を使用してメールを作成するデモ'
    },
    bugReport: {
      title: 'インテリジェントバグレポート',
      description: 'ワンクリックで包括的なバグレポートを生成。GitHub Issues、JIRAなどのプラットフォームに対応し、私たちのAIがあなたの簡潔な説明を詳細で構造化されたレポートに変換し、チームのコミュニケーションを効果的にサポートします。',
      alt: 'FillifyがGitHub Issues用の詳細なバグレポートを生成する例'
    },
    aiProvider: {
      title: 'AIプロバイダーを選択',
      description: 'OpenAI、Anthropic Claude、Google Gemini、Moonshot AIなど、複数のAIプロバイダーに対応。APIキーを入力するだけで、お好みのAIモデルを利用できます。プロバイダーの切り替えも簡単で、自由にカスタマイズが可能です。',
      alt: 'FillifyでさまざまなAIプロバイダーを選択できるインターフェース'
    }
  },
  faq: {
    title: 'よくある質問',
    items: {
      what: {
        question: 'Fillifyとは？',
        answer: 'FillifyはAIを活用したChrome拡張機能で、一文入力するだけでウェブフォームを瞬時に記入できます。登録フォーム、バグレポート、メールなど、あらゆるフォームを正確かつインテリジェントに入力します。'
      },
      types: {
        question: 'Fillifyはどんなフォームに対応していますか？',
        answer: '一般的なウェブフォーム、バグレポート、メール入力などに対応。テキストフィールドやテキストエリアなど、さまざまなウェブサイトでスムーズに自動入力を行います。'
      },
      providers: {
        question: 'FillifyはどのAIプロバイダーをサポートしていますか？',
        answer: 'FillifyはOpenAI、Anthropic Claude、Google Gemini、Moonshot AIなど、複数のAIプロバイダーと統合されています。プロバイダーを簡単に切り替えたり、自身のAPIキーを使用したりすることで、最大限の柔軟性とコントロールが可能です。'
      },
      privacy: {
        question: 'Fillifyはデータとプライバシーをどのように保護しますか？',
        answer: "私たちは、お客様のプライバシーとデータの安全を非常に重視しています。APIキーはブラウザにローカル保存され、リクエスト時にのみ当社のサーバーへ送信され、暗号化された後、選択したAIプロバイダーに転送されます。私たちは APIキーを保存・悪用したり、第三者と共有したりすることは決してありませんので、ご安心ください。"
      },
      customize: {
        question: '特定のフォームに合わせてAIの入力をカスタマイズできますか？',
        answer: 'はい！バグレポートモードでは、事前に設定したテンプレートを作成し、より正確で一貫性のあるレポートを生成できます。'
      },
      languages: {
        question: 'Fillifyはどの言語に対応していますか？',
        answer: 'Fillifyは複数の言語をサポートし、フォームの言語を自動検出できます。また、拡張機能のポップアップから手動で出力言語を選択することも可能です。'
      }
    }
  },
  bottomCta: {
    subtitle: 'ワークフローを変革する準備はできましたか？',
    title: '次世代のフォーム入力を体験しよう',
    button: '今すぐインストール'
  },
  footer: {
    copyright: '© {year} Fillify. All rights reserved.',
    social: {
      twitter: 'X（Twitter）',
      youtube: 'YouTube'
    },
    links: {
      terms: '利用規約',
      privacy: 'プライバシーポリシー'
    }
  },
  signin: {
    title: 'Fillifyへようこそ',
    subtitle: 'AIフォーム入力拡張機能を利用するにはサインインしてください',
    email: {
      label: 'メールアドレス',
      placeholder: 'メールアドレスを入力してください',
      continue: 'メールアドレスで続ける',
      sending: '送信中...'
    },
    otp: {
      sentMessage: '以下のメールアドレスに確認コードを送信しました',
      label: 'ログインコード',
      placeholder: '確認コードを入力してください',
      continue: 'ログインコードで続ける',
      verifying: '確認中...',
      resend: {
        resending: '再送信中...',
        cooldown: '{seconds}秒後に再送信',
        action: '確認コードを再送信'
      }
    },
    divider: 'または',
    error: {
      invalidEmail: '有効なメールアドレスを入力してください',
      sendOTP: '確認コードの送信に失敗しました。もう一度お試しください。',
      invalidOTP: '6桁の有効な確認コードを入力してください',
      verifyOTP: '確認コードが無効または期限切れです。もう一度お試しください。',
      googleLogin: 'Googleログインに失敗しました。もう一度お試しください。'
    },
    features: {
      title: 'サインインするとできること:',
      list: {
        autoFill: 'AIによるフォーム自動入力',
        api: '独自のAPIカスタマイズ',
        early: '新機能への先行アクセス'
      }
    },
    terms: {
      prefix: 'サインインすることで、',
      and: 'および',
      termsOfService: '利用規約',
      privacyPolicy: 'プライバシーポリシー'
    },
    seo: {
      title: 'サインイン - Fillify',
      description: 'AIによるフォーム入力機能を利用するためにFillifyにサインイン'
    }
  },
  meta: {
    title: 'Fillify - フォーム入力、メール作成、バグレポート作成のAIアシスタント',
    description: 'FillifyはAI技術でフォーム入力を革新します。ウェブフォームの自動入力、メールの作成、バグレポートの生成をインテリジェントな自動化で実現します。',
    keywords: {
      formFilling: 'AIフォーム入力',
      automation: 'AI自動化',
      email: 'AIメール生成',
      bugReport: 'AIバグレポート生成',
      additional: [
        'スマートフォーム入力',
        '自動データ入力',
        'AIフォームアシスタント',
        'インテリジェントフォーム入力',
        'Chromeフォーム自動入力',
        'AIフォームフィラー'
      ]
    }
  },
  privacy: {
    meta: {
      title: 'プライバシーポリシー - Fillify',
      description: 'Fillifyがプライバシーをどのように保護し、データを管理しているかをご確認ください。'
    },
    title: 'プライバシーポリシー',
    lastUpdated: '最終更新日: {date}'
  },
  terms: {
    meta: {
      title: '利用規約 - Fillify',
      description: 'Fillifyのサービス利用に関する規約をご確認ください。'
    },
    title: '利用規約',
    lastUpdated: '最終更新日: {date}'
  },
  dashboard: {
    meta: {
      title: 'ダッシュボード - Fillify',
      description: 'Fillifyアカウントの管理、現在のプランの確認、使用状況の追跡ができます。'
    },
    currentPlan: '現在のプラン',
    settings: '設定',
    usageOverview: '使用状況の概要',
    creditsUsed: '使用済みクレジット'
  },
  blog: {
    meta: {
      title: 'ブログ - Fillify',
      description: 'AIによるフォーム入力と生産性自動化に関する最新ニュース、アップデート、ヒントをお読みください。'
    },
    hero: {
      badge: '最新情報',
      title: 'ブログ',
      subtitle: '最新ニュース、リリース、ヒント'
    },
    list: {
      readMore: '続きを読む',
      publishedOn: '公開日',
      minRead: '分で読める',
      noPostsTitle: 'まだ投稿がありません',
      noPostsDescription: '素晴らしいコンテンツを作成中です。しばらくしてからまた確認してください。'
    },
    article: {
      backToBlog: 'ブログに戻る',
      thanksTitle: 'お読みいただきありがとうございます！',
      thanksDescription: 'Fillifyについてご質問やご提案がございましたら、お気軽にお問い合わせください。',
      tryFillify: 'Fillifyを試す',
      moreArticles: 'その他の記事',
      notFoundTitle: '記事が見つかりません',
      notFoundDescription: '申し訳ございませんが、お探しの記事は存在しないか削除されています。',
      backToBlogBtn: 'ブログに戻る'
    }
  },
  '404': {
    title: 'ページが見つかりません',
    description: '申し訳ありませんが、お探しのページが見つかりません。URLをご確認いただくか、ホームページにお戻りください。',
    backHome: 'ホームに戻る'
  }
}