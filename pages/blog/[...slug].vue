<template>
  <div class="min-h-screen relative">
    <!-- Background with gradient -->
    <div class="absolute inset-0 bg-gradient-to-b from-blue-50 to-white"></div>
    

    
    <main class="relative">
      <article class="container mx-auto px-4 py-16 max-w-4xl">
        <template v-if="doc">
          <!-- Navigation -->
          <nav class="mb-8 animate-slide-up">
            <NuxtLink 
              :to="localePath('/blog')" 
              class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors group"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left group-hover:-translate-x-1 transition-transform">
                <path d="M12 19l-7-7 7-7"/>
                <path d="M19 12H5"/>
              </svg>
              {{ t('blog.article.backToBlog') }}
            </NuxtLink>
          </nav>

          <!-- Article Header -->
          <header class="mb-12 animate-slide-up [animation-delay:200ms]">
            <!-- Tags -->
            <div v-if="Array.isArray(doc.tags) && doc.tags.length" class="flex flex-wrap gap-2 mb-6">
              <span 
                v-for="tag in doc.tags" 
                :key="tag" 
                class="px-3 py-1 text-sm font-medium bg-blue-50 text-blue-600 rounded-full border border-blue-100"
              >
                #{{ tag }}
              </span>
            </div>

            <!-- Title -->
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 mb-6 leading-tight">
              {{ doc.title }}
            </h1>

            <!-- Description -->
            <p v-if="doc.description" class="text-xl md:text-2xl text-gray-600 leading-relaxed mb-8">
              {{ doc.description }}
            </p>

            <!-- Meta Info -->
            <div class="flex items-center gap-6 pt-6 border-t border-gray-200">
              <div class="flex items-center gap-2 text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar">
                  <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                  <line x1="16" x2="16" y1="2" y2="6"/>
                  <line x1="8" x2="8" y1="2" y2="6"/>
                  <line x1="3" x2="21" y1="10" y2="10"/>
                </svg>
                <span class="font-medium">{{ formatDate(doc.date) }}</span>
              </div>
              
              <div class="flex items-center gap-2 text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock">
                  <circle cx="12" cy="12" r="10"/>
                  <polyline points="12,6 12,12 16,14"/>
                </svg>
                <span class="font-medium">{{ getReadTime(doc.body) }} {{ t('blog.list.minRead') }}</span>
              </div>
            </div>
          </header>

          <!-- Article Content -->
          <div class="animate-slide-up [animation-delay:400ms]">
            <!-- Content Container -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 p-8 md:p-12 shadow-xl shadow-blue-500/5">
              <div class="prose prose-lg prose-neutral max-w-none prose-headings:scroll-mt-20 prose-headings:font-bold prose-p:leading-relaxed prose-p:text-gray-700 prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline prose-strong:text-gray-900 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-blockquote:border-l-blue-500 prose-blockquote:bg-blue-50/50 prose-blockquote:pl-6 prose-blockquote:py-4 prose-blockquote:rounded-r prose-ul:list-disc prose-ol:list-decimal prose-li:marker:text-blue-500 prose-table:border-collapse prose-th:border prose-th:border-gray-300 prose-th:bg-gray-50 prose-th:px-4 prose-th:py-2 prose-td:border prose-td:border-gray-300 prose-td:px-4 prose-td:py-2">
                <ContentRenderer :value="doc" />
              </div>
            </div>
          </div>

          <!-- Article Footer -->
          <footer class="mt-12 animate-slide-up [animation-delay:600ms]">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
              <div class="text-center">
                <h3 class="text-xl font-bold text-gray-900 mb-4">{{ t('blog.article.thanksTitle') }}</h3>
                <p class="text-gray-600 mb-6">{{ t('blog.article.thanksDescription') }}</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="inline-flex items-center justify-center px-6 py-3 text-white bg-blue-600 rounded-xl hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl group"
                  >
                    {{ t('blog.article.tryFillify') }}
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link ml-2 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform">
                      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                      <polyline points="15,3 21,3 21,9"/>
                      <line x1="10" x2="21" y1="14" y2="3"/>
                    </svg>
                  </a>
                  <NuxtLink
                    :to="localePath('/blog')"
                    class="inline-flex items-center justify-center px-6 py-3 text-blue-600 bg-white rounded-xl hover:bg-gray-50 transition-colors border border-blue-200 group"
                  >
                    {{ t('blog.article.moreArticles') }}
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right ml-2 group-hover:translate-x-0.5 transition-transform">
                      <path d="M5 12h14"/>
                      <path d="M12 5l7 7-7 7"/>
                    </svg>
                  </NuxtLink>
                </div>
              </div>
            </div>
          </footer>
        </template>
        
        <!-- Not Found State -->
        <div v-else class="text-center py-20">
          <div class="max-w-md mx-auto">
            <div class="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-x text-red-500">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="9.5" x2="14.5" y1="12.5" y2="17.5"/>
                <line x1="14.5" x2="9.5" y1="12.5" y2="17.5"/>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">{{ t('blog.article.notFoundTitle') }}</h3>
            <p class="text-gray-600 mb-8">{{ t('blog.article.notFoundDescription') }}</p>
            <NuxtLink
              :to="localePath('/blog')"
              class="inline-flex items-center justify-center px-6 py-3 text-white bg-blue-600 rounded-xl hover:bg-blue-700 transition-colors"
            >
              {{ t('blog.article.backToBlogBtn') }}
            </NuxtLink>
          </div>
        </div>
      </article>
    </main>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
const localePath = useLocalePath()
const { t } = useI18n()
const contentPath = computed(() => {
  const slug = route.params.slug
  const parts = Array.isArray(slug) ? slug : [String(slug)]
  return `/blog/${parts.join('/')}`
})

const { data: doc } = await useAsyncData(
  'blog-doc-' + (Array.isArray(route.params.slug) ? route.params.slug.join('/') : String(route.params.slug || '')),
  () => queryContent(contentPath.value).findOne(),
  { watch: [contentPath] }
)

// 为博客文章设置动态SEO
watch(doc, (newDoc) => {
  if (newDoc) {
    useHead({
      title: `${newDoc.title} - Fillify Blog`,
      meta: [
        { name: 'description', content: newDoc.description || `Read Fillify blog article: ${newDoc.title}` },
        { name: 'keywords', content: (newDoc.tags || []).join(', ') + ', Fillify, AI, form filling, automation, productivity, Chrome extension' },
        { property: 'og:title', content: `${newDoc.title} - Fillify Blog` },
        { property: 'og:description', content: newDoc.description || `Read Fillify blog article: ${newDoc.title}` },
        { property: 'og:type', content: 'article' },
        { property: 'og:url', content: `https://fillify.tech${contentPath.value}` },
        { property: 'article:published_time', content: newDoc.date },
        { property: 'article:author', content: 'Fillify Team' },
        { property: 'article:section', content: 'Technology' },
        { property: 'article:tag', content: (newDoc.tags || []).join(', ') }
      ]
    })
  }
}, { immediate: true })

function formatDate(value?: string) {
  if (!value) return ''
  const d = new Date(value)
  if (Number.isNaN(d.getTime())) return ''
  return d.toLocaleDateString('en-US', {
    year: 'numeric', 
    month: 'long', 
    day: 'numeric'
  })
}

// 估算阅读时间（基于中文阅读速度约 300-500 字/分钟）
function getReadTime(body: any): number {
  if (!body || !body.children) return 1
  
  const getTextContent = (node: any): string => {
    if (node.type === 'text') {
      return node.value || ''
    }
    if (node.children && Array.isArray(node.children)) {
      return node.children.map(getTextContent).join('')
    }
    return ''
  }
  
  const totalText = getTextContent(body)
  const wordCount = totalText.length
  const readTime = Math.max(1, Math.ceil(wordCount / 400)) // 假设中文阅读速度 400 字/分钟
  
  return readTime
}
</script>

<style scoped>
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out forwards;
  opacity: 0;
}

/* 为长内容添加行限制 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
