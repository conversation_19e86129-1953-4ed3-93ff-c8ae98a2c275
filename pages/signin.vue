<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white flex items-center justify-center">
    <!-- Main Content -->
    <main class="w-full px-4 flex flex-col items-center">
      <div class="max-w-md w-full">
        <!-- Sign in card -->
        <div class="bg-white/80 backdrop-blur-sm py-8 px-4 shadow-lg rounded-lg sm:px-10">
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900">{{ $t('signin.title') }}</h2>
            <p class="mt-2 text-sm text-gray-600">
              {{ $t('signin.subtitle') }}
            </p>
          </div>

          <!-- Email Login Section -->
          <div class="mb-6">
            <!-- Email Input -->
            <div v-if="!otpSent">
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('signin.email.label') }}
              </label>
              <input
                id="email"
                v-model="email"
                type="email"
                required
                :disabled="isLoading"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                :placeholder="$t('signin.email.placeholder')"
              />
              <button
                @click="sendOTP"
                :disabled="isLoading || !email || !isValidEmail"
                class="w-full mt-3 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isLoading" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {{ $t('signin.email.sending') }}
                </span>
                <span v-else>{{ $t('signin.email.continue') }}</span>
              </button>
            </div>

            <!-- OTP Input -->
            <div v-else>
              <div class="text-center mb-4">
                <p class="text-sm text-gray-600">
                  {{ $t('signin.otp.sentMessage') }}
                </p>
                <p class="text-sm font-medium text-gray-900">{{ email }}</p>
              </div>

              <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('signin.otp.label') }}
              </label>
              <input
                id="otp"
                v-model="otp"
                type="text"
                required
                maxlength="6"
                :disabled="isLoading"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 text-center text-lg tracking-widest"
                :placeholder="$t('signin.otp.placeholder')"
              />

              <button
                @click="verifyOTP"
                :disabled="isLoading || !otp || otp.length !== 6"
                class="w-full mt-3 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isLoading" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {{ $t('signin.otp.verifying') }}
                </span>
                <span v-else>{{ $t('signin.otp.continue') }}</span>
              </button>

              <!-- Resend Code -->
              <div class="text-center mt-3">
                <button
                  @click="resendOTP"
                  :disabled="isResending || resendCooldown > 0"
                  class="text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="isResending" class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-1"></div>
                    {{ $t('signin.otp.resend.resending') }}
                  </span>
                  <span v-else-if="resendCooldown > 0">
                    {{ $t('signin.otp.resend.cooldown', { seconds: resendCooldown }) }}
                  </span>
                  <span v-else>{{ $t('signin.otp.resend.action') }}</span>
                </button>
              </div>
            </div>

            <!-- Error message -->
            <div v-if="errorMessage" class="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
              <p class="text-sm text-red-600">{{ errorMessage }}</p>
            </div>
          </div>

          <!-- Divider and Google Login (only show when not in OTP verification mode) -->
          <div v-if="!otpSent">
            <!-- Divider -->
            <div class="relative mb-6">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300" />
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">{{ $t('signin.divider') }}</span>
              </div>
            </div>

            <!-- Google Sign In Button -->
            <div class="mb-6">
              <div
                id="googleSignInButton"
                class="flex justify-center w-full max-w-[320px] mx-auto"
                :class="{ 'opacity-50 pointer-events-none': isGoogleLoading }"
              ></div>
            </div>
          </div>

          <!-- Features -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-sm font-medium text-gray-500 mb-4">{{ $t('signin.features.title') }}</h3>
            <ul class="space-y-4">
              <li class="flex items-center text-sm text-gray-600">
                <Sparkles class="h-5 w-5 text-blue-500 mr-3" />
                {{ $t('signin.features.list.autoFill') }}
              </li>
              <li class="flex items-center text-sm text-gray-600">
                <Wand class="h-5 w-5 text-purple-500 mr-3" />
                {{ $t('signin.features.list.api') }}
              </li>
              <li class="flex items-center text-sm text-gray-600">
                <Rocket class="h-5 w-5 text-orange-500 mr-3" />
                {{ $t('signin.features.list.early') }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Terms and Privacy -->
        <div class="mt-6 text-center text-xs text-gray-500">
          <span class="block">{{ $t('signin.terms.prefix') }}</span>
          <span class="mt-1 inline-block">
            <NuxtLink to="/terms" class="text-blue-600 hover:text-blue-800">
              {{ $t('signin.terms.termsOfService') }}
            </NuxtLink>
            <span class="mx-1">{{ $t('signin.terms.and') }}</span>
            <NuxtLink to="/privacy" class="text-blue-600 hover:text-blue-800">
              {{ $t('signin.terms.privacyPolicy') }}
            </NuxtLink>
          </span>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, inject, watch, computed } from 'vue'
import { cookieUtil } from '~/utils/cookie'
import { Sparkles, Wand, Rocket } from 'lucide-vue-next'
import { useAuth } from '~/composables/useAuth'

// Type definitions
interface UserState {
  isLoggedIn: boolean
  credits: number
  userId: string | null
}

// Declare google global variables
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void
          renderButton: (element: HTMLElement | null, options: any) => void
          disableAutoSelect: () => void
          revoke: (email: string, callback: () => void) => void
        }
      }
    }
  }
}

const { locale, t } = useI18n()
const { user, isLoggedIn, fetchUserData, verifyEmailOTP: authVerifyOTP } = useAuth()
const isGoogleScriptLoaded = ref(false)
const isLoading = ref(false)
const config = useRuntimeConfig()
const googleClientId = config.public.GOOGLE_CLIENT_ID

// Get type-safe userState
const userState = inject<UserState>('userState')

// Email OTP login related state
const email = ref('')
const otp = ref('')
const otpSent = ref(false)
const errorMessage = ref('')
const resendCooldown = ref(0)
const isResending = ref(false)
const isGoogleLoading = ref(false)

// Email format validation
const isValidEmail = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.value)
})

// 确保在使用前检查 googleClientId
if (!googleClientId && import.meta.dev) {
  console.error('Google Client ID is not configured')
}

// Watch login status
watch(isLoggedIn, (newValue) => {
  if (newValue) {
    const currentLang = locale.value
    const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
    navigateTo(dashboardPath)
  }
}, { immediate: true })

// Send OTP
const sendOTP = async () => {
  if (!email.value || !isValidEmail.value) {
    errorMessage.value = t('signin.error.invalidEmail')
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const response = await $fetch('/api/auth/send-otp', {
      method: 'POST',
      body: {
        email: email.value
      }
    })

    if (response.success) {
      otpSent.value = true
      startResendCooldown()
    }
  } catch (error: any) {
    console.error('Send OTP error:', error)
    errorMessage.value = error.data?.message || t('signin.error.sendOTP')
  } finally {
    isLoading.value = false
  }
}

// Resend OTP (separate function, doesn't affect main button loading state)
const resendOTP = async () => {
  if (!email.value || !isValidEmail.value) {
    errorMessage.value = t('signin.error.invalidEmail')
    return
  }

  isResending.value = true
  errorMessage.value = ''

  try {
    const response = await $fetch('/api/auth/send-otp', {
      method: 'POST',
      body: {
        email: email.value
      }
    })

    if (response.success) {
      startResendCooldown()
    }
  } catch (error: any) {
    console.error('Resend OTP error:', error)
    errorMessage.value = error.data?.message || t('signin.error.sendOTP')
  } finally {
    isResending.value = false
  }
}

// Verify OTP and login
const verifyOTP = async () => {
  if (!otp.value || otp.value.length !== 6) {
    errorMessage.value = t('signin.error.invalidOTP')
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const response = await authVerifyOTP(email.value, otp.value)

    if (response.success && response.user) {
      // Set cookie for auth
      cookieUtil.setCookie('xToken', response.user.id, {
        expires: 7,
        path: '/'
      })

      // Wait for state update to complete
      await nextTick()
      // Force refresh user data
      await fetchUserData()

      // 导航到仪表板
      try {
        const currentLang = locale.value
        const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
        await navigateTo(dashboardPath, { replace: true })
      } catch (navError) {
        console.error('Navigation error:', navError)
        const localePath = useLocalePath()
        window.location.href = localePath('/dashboard')
      }
    }
  } catch (error: any) {
    console.error('Verify OTP error:', error)
    errorMessage.value = error.data?.message || t('signin.error.verifyOTP')
  } finally {
    isLoading.value = false
  }
}

// Start resend cooldown
const startResendCooldown = () => {
  resendCooldown.value = 60
  const timer = setInterval(() => {
    resendCooldown.value--
    if (resendCooldown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// Handle Google login callback
const handleCredentialResponse = async (response: any) => {
  console.log("Encoded JWT ID token: " + response.credential)
  isGoogleLoading.value = true
  errorMessage.value = ''

  try {
    const res = await fetch(`${config.public.apiBase}/api/auth/google-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ credential: response.credential }),
    })
    const data = await res.json()

    if (data.success) {
      // Set user state using useAuth
      user.value = data.user
      cookieUtil.setCookie('xToken', data.user.id, {
        expires: 7,
        path: '/'
      })

      // Wait for state update to complete
      await nextTick()
      // Force refresh user data to sync all states
      await fetchUserData()

      // Use try-catch to ensure navigation succeeds
      try {
        const currentLang = locale.value
        const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
        await navigateTo(dashboardPath, { replace: true })
      } catch (navError) {
        console.error('Navigation error:', navError)
        // If navigation fails, use window.location
        const localePath = useLocalePath()
        const dashboardPath = localePath('/dashboard')
        window.location.href = dashboardPath
      }
    }
  } catch (error) {
    console.error('Google login error:', error)
    errorMessage.value = t('signin.error.googleLogin')
  } finally {
    isGoogleLoading.value = false
  }
}

// 确保在使用前检查 googleClientId
if (!googleClientId && import.meta.dev) {
  console.error('Google Client ID is not configured')
}

// Load Google Sign-In button
onMounted(() => {
  if (googleClientId) {
    const script = document.createElement('script')
    script.src = 'https://accounts.google.com/gsi/client'
    script.onload = () => {
      if (window.google?.accounts?.id) {
        window.google.accounts.id.initialize({
          client_id: googleClientId,
          callback: handleCredentialResponse,
        })
        const buttonElement = document.getElementById('googleSignInButton')
        if (buttonElement) {
          window.google.accounts.id.renderButton(
            buttonElement,
            { theme: 'outline', size: 'large', width: 320 }
          )
        }
        isGoogleScriptLoaded.value = true
      }
    }
    document.head.appendChild(script)
  }
})

// SEO
useHead({
  title: () => t('signin.seo.title'),
  meta: [
    { name: 'description', content: () => t('signin.seo.description') },
  ],
})
</script>