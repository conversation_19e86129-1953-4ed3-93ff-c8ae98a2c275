<template>
  <div class="min-h-screen relative">
    <!-- Background Color -->
    <div class="absolute inset-0 bg-gradient-to-b from-blue-50 to-white"></div>
    
    <!-- Main Content Wrapper -->
    <main class="relative pt-8">
      <!-- Hero Section -->
      <section class="container mx-auto px-4 py-16 md:py-20 relative overflow-hidden">
        <!-- Hero Background Grid -->
        <div class="absolute inset-0 -mx-4" style="
          background-image: linear-gradient(to right, #e5efff 1px, transparent 1px), linear-gradient(to bottom, #e5efff 1px, transparent 1px); 
          background-size: 4rem 4rem;
          mask-image: radial-gradient(ellipse 80% 50% at 50% 50%, black 40%, transparent 100%);
          -webkit-mask-image: radial-gradient(ellipse 80% 50% at 50% 50%, black 40%, transparent 100%);
        "></div>
        
        <div class="max-w-6xl mx-auto text-center space-y-6 relative">
          <!-- Gradient Accent -->
          <div class="absolute top-0 left-1/2 -translate-x-1/2 h-[600px] w-[600px] bg-blue-500/20 rounded-full blur-[120px] opacity-20 -z-[1]"></div>
          
          <!-- Small Heading -->
          <div class="inline-block animate-slide-up">
            <div class="flex items-center gap-2 px-3 py-1 text-sm rounded-full border border-blue-200 bg-blue-50/80 text-blue-600 font-medium backdrop-blur-sm">
              <span class="relative flex h-2 w-2">
                <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                <span class="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
              </span>
              {{ t('hero.chromeStore') }}
            </div>
          </div>

          <!-- Main Heading -->
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight text-gray-900 animate-slide-up [animation-delay:200ms]">
            <span class="block">{{ t('hero.title.text') }}</span>
            <transition name="fade" mode="out-in">
              <span :key="currentWord" class="bg-gradient-to-r from-blue-600 to-indigo-600 text-transparent bg-clip-text block min-h-[1.2em] flex items-center justify-center">
                {{ currentWord }}
              </span>
            </transition>
          </h1>

          <!-- Description -->
          <p class="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-slide-up [animation-delay:400ms]">
            {{ t('hero.description') }}
          </p>

          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center pt-4 animate-slide-up [animation-delay:600ms]">
            <a
              href="https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn"
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-blue-600 rounded-xl hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl group"
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                viewBox="0 0 48 48" 
                class="w-6 h-6 mr-2 flex-shrink-0"
                role="img"
                aria-label="Chrome Browser Logo"
              >
                <path fill="#fff" d="M34,24c0,5.521-4.479,10-10,10s-10-4.479-10-10s4.479-10,10-10S34,18.479,34,24z"/>
                <linearGradient id="Pax8JcnMzivu8f~SZ~k1ya" x1="5.789" x2="31.324" y1="34.356" y2="20.779" gradientTransform="matrix(1 0 0 -1 0 50)" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#4caf50"/>
                  <stop offset=".489" stop-color="#4aaf50"/>
                  <stop offset=".665" stop-color="#43ad50"/>
                  <stop offset=".79" stop-color="#38aa50"/>
                  <stop offset=".892" stop-color="#27a550"/>
                  <stop offset=".978" stop-color="#11a050"/>
                  <stop offset="1" stop-color="#0a9e50"/>
                </linearGradient>
                <path fill="url(#Pax8JcnMzivu8f~SZ~k1ya)" d="M31.33,29.21l-8.16,14.77C12.51,43.55,4,34.76,4,24C4,12.96,12.96,4,24,4v11 c-4.97,0-9,4.03-9,9s4.03,9,9,9C27.03,33,29.7,31.51,31.33,29.21z"/>
                <linearGradient id="Pax8JcnMzivu8f~SZ~k1yb" x1="33.58" x2="33.58" y1="6" y2="34.797" gradientTransform="matrix(1 0 0 -1 0 50)" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#ffd747"/>
                  <stop offset=".482" stop-color="#ffd645"/>
                  <stop offset=".655" stop-color="#fed43e"/>
                  <stop offset=".779" stop-color="#fccf33"/>
                  <stop offset=".879" stop-color="#fac922"/>
                  <stop offset=".964" stop-color="#f7c10c"/>
                  <stop offset="1" stop-color="#f5bc00"/>
                </linearGradient>
                <path fill="url(#Pax8JcnMzivu8f~SZ~k1yb)" d="M44,24c0,11.05-8.95,20-20,20h-0.84l8.17-14.79C32.38,27.74,33,25.94,33,24 c0-4.97-4.03-9-9-9V4c7.81,0,14.55,4.48,17.85,11C43.21,17.71,44,20.76,44,24z"/>
                <linearGradient id="Pax8JcnMzivu8f~SZ~k1yc" x1="36.128" x2="11.574" y1="44.297" y2="28.954" gradientTransform="matrix(1 0 0 -1 0 50)" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#f7572f"/>
                  <stop offset=".523" stop-color="#f7552d"/>
                  <stop offset=".712" stop-color="#f75026"/>
                  <stop offset=".846" stop-color="#f7461b"/>
                  <stop offset=".954" stop-color="#f7390a"/>
                  <stop offset="1" stop-color="#f73100"/>
                </linearGradient>
                <path fill="url(#Pax8JcnMzivu8f~SZ~k1yc)" d="M41.84,15H24c-4.97,0-9,4.03-9,9c0,1.49,0.36,2.89,1.01,4.13H16L7.16,13.26H7.14 C10.68,7.69,16.91,4,24,4C31.8,4,38.55,8.48,41.84,15z"/>
                <linearGradient id="Pax8JcnMzivu8f~SZ~k1yd" x1="19.05" x2="28.95" y1="30.95" y2="21.05" gradientTransform="matrix(1 0 0 -1 0 50)" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#2aa4f4"/>
                  <stop offset="1" stop-color="#007ad9"/>
                </linearGradient>
                <path fill="url(#Pax8JcnMzivu8f~SZ~k1yd)" d="M31,24c0,3.867-3.133,7-7,7s-7-3.133-7-7s3.133-7,7-7S31,20.133,31,24z"/>
              </svg>
              {{ t('hero.cta.chrome') }}
              <ArrowRight class="ml-2 h-5 w-5 group-hover:translate-x-0.5 transition-transform" />
            </a>
            <a
              href="#features"
              class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200 shadow-lg hover:shadow-xl group"
              @click.prevent="scrollToFeatures"
            >
              {{ t('hero.cta.learnMore') }}
              <ArrowDown class="ml-2 h-5 w-5 group-hover:translate-y-0.5 transition-transform" />
            </a>
          </div>

          <!-- Badges -->
          <div class="flex flex-wrap items-center justify-center gap-8 mt-12">
            <a 
              href="https://www.saashub.com/fillify"
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center justify-center transition-all duration-200 hover:opacity-90"
            >
              <img
                src="https://cdn-b.saashub.com/img/badges/approved-color.png?v=1"
                alt="Fillify badge"
                class="h-14 w-[200px] object-contain"
              />
            </a>
            <a 
              href="https://startupfa.me/s/fillify?utm_source=fillify.tech" 
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center justify-center transition-all duration-200 hover:opacity-90"
            >
              <img 
                src="https://startupfa.me/badges/featured-badge.webp" 
                alt="Fillify - AI-Powered Forms, Emails & Bug Reports Assistant | Startup Fame" 
                width="171" 
                height="54"
                class="h-14 w-[200px] object-contain"
              />
            </a>
            <a 
              href="https://twelve.tools" 
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center justify-center transition-all duration-200 hover:opacity-90"
            >
              <img 
                src="https://twelve.tools/badge0-light.svg" 
                alt="Featured on Twelve Tools" 
                class="h-14 w-[200px] object-contain"
              />
            </a>
          </div>

          <!-- Stats -->
          <div class="grid grid-cols-2 md:grid-cols-3 gap-8 max-w-3xl mx-auto pt-12 animate-slide-up [animation-delay:800ms]" ref="statsSection">
            <div class="text-center">
              <div class="text-4xl font-bold text-blue-600">{{ formCount }}{{ t('stats.forms.value') }}</div>
              <div class="text-gray-600 mt-1">{{ t('stats.forms.label') }}</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-blue-600">{{ accuracyRate }}{{ t('stats.accuracy.value') }}</div>
              <div class="text-gray-600 mt-1">{{ t('stats.accuracy.label') }}</div>
            </div>
            <div class="text-center md:col-span-1 col-span-2">
              <div class="text-4xl font-bold text-blue-600">{{ t('stats.support.value') }}</div>
              <div class="text-gray-600 mt-1">{{ t('stats.support.label') }}</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section 
        id="features" 
        ref="featuresSection"
        class="scroll-mt-8"
      >
        <div class="text-center py-16 md:py-24">
          <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            {{ t('features.title') }}
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto px-4">
            {{ t('features.subtitle') }}
          </p>
        </div>

        <FeatureSection
          :icon="ClipboardList"
          :title="t('features.formFilling.title')"
          :description="t('features.formFilling.description')"
          image="/images/features/form-filling.png"
          translationKey="formFilling"
        />
        
        <FeatureSection
          :icon="Mail"
          :title="t('features.email.title')"
          :description="t('features.email.description')"
          image="/images/features/email-assistant.png"
          :reverse="true"
          translationKey="email"
        />
        
        <FeatureSection
          :icon="Bug"
          :title="t('features.bugReport.title')"
          :description="t('features.bugReport.description')"
          image="/images/features/bug-report.png"
          translationKey="bugReport"
        />

        <FeatureSection
          :icon="Settings"
          :title="t('features.aiProvider.title')"
          :description="t('features.aiProvider.description')"
          image="/images/features/ai-providers.png"
          :reverse="true"
          translationKey="aiProvider"
        />
      </section>

      <!-- FAQs Section -->
      <section class="container mx-auto px-4 py-16 md:py-20" ref="faqSection">
        <div class="max-w-4xl mx-auto">
          <h2 class="text-3xl md:text-4xl font-bold text-center mb-12 animate-slide-up [animation-delay:200ms]">
            {{ t('faq.title') }}
          </h2>
          
          <div class="space-y-4">
            <!-- Question 1 -->
            <div class="border-b border-gray-200 animate-slide-up [animation-delay:400ms]">
              <button 
                class="w-full py-6 text-left focus:outline-none group"
                @click="toggleFaq(0)"
              >
                <div class="flex items-center justify-between">
                  <h3 class="text-xl font-semibold text-gray-900">{{ t('faq.items.what.question') }}</h3>
                  <span class="ml-6 flex-shrink-0">
                    <svg 
                      class="w-6 h-6 transform transition-transform duration-200" 
                      :class="openFaq.includes(0) ? 'rotate-180' : ''"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                </div>
              </button>
              <div 
                class="transition-all duration-200 overflow-hidden"
                :class="openFaq.includes(0) ? 'max-h-96 pb-6' : 'max-h-0'"
              >
                <p class="text-lg text-gray-600">{{ t('faq.items.what.answer') }}</p>
              </div>
            </div>

            <!-- Question 2 -->
            <div class="border-b border-gray-200 animate-slide-up [animation-delay:600ms]">
              <button 
                class="w-full py-6 text-left focus:outline-none group"
                @click="toggleFaq(1)"
              >
                <div class="flex items-center justify-between">
                  <h3 class="text-xl font-semibold text-gray-900">{{ t('faq.items.types.question') }}</h3>
                  <span class="ml-6 flex-shrink-0">
                    <svg 
                      class="w-6 h-6 transform transition-transform duration-200" 
                      :class="openFaq.includes(1) ? 'rotate-180' : ''"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                </div>
              </button>
              <div 
                class="transition-all duration-200 overflow-hidden"
                :class="openFaq.includes(1) ? 'max-h-96 pb-6' : 'max-h-0'"
              >
                <p class="text-lg text-gray-600">{{ t('faq.items.types.answer') }}</p>
              </div>
            </div>

            <!-- Question 3 -->
            <div class="border-b border-gray-200 animate-slide-up [animation-delay:800ms]">
              <button 
                class="w-full py-6 text-left focus:outline-none group"
                @click="toggleFaq(2)"
              >
                <div class="flex items-center justify-between">
                  <h3 class="text-xl font-semibold text-gray-900">{{ t('faq.items.providers.question') }}</h3>
                  <span class="ml-6 flex-shrink-0">
                    <svg 
                      class="w-6 h-6 transform transition-transform duration-200" 
                      :class="openFaq.includes(2) ? 'rotate-180' : ''"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                </div>
              </button>
              <div 
                class="transition-all duration-200 overflow-hidden"
                :class="openFaq.includes(2) ? 'max-h-96 pb-6' : 'max-h-0'"
              >
                <p class="text-lg text-gray-600">{{ t('faq.items.providers.answer') }}</p>
              </div>
            </div>

            <!-- Question 4 -->
            <div class="border-b border-gray-200 animate-slide-up [animation-delay:1000ms]">
              <button 
                class="w-full py-6 text-left focus:outline-none group"
                @click="toggleFaq(3)"
              >
                <div class="flex items-center justify-between">
                  <h3 class="text-xl font-semibold text-gray-900">{{ t('faq.items.privacy.question') }}</h3>
                  <span class="ml-6 flex-shrink-0">
                    <svg 
                      class="w-6 h-6 transform transition-transform duration-200" 
                      :class="openFaq.includes(3) ? 'rotate-180' : ''"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                </div>
              </button>
              <div 
                class="transition-all duration-200 overflow-hidden"
                :class="openFaq.includes(3) ? 'max-h-96 pb-6' : 'max-h-0'"
              >
                <p class="text-lg text-gray-600">{{ t('faq.items.privacy.answer') }}</p>
              </div>
            </div>

            <!-- Question 5 -->
            <div class="border-b border-gray-200 animate-slide-up [animation-delay:1200ms]">
              <button 
                class="w-full py-6 text-left focus:outline-none group"
                @click="toggleFaq(4)"
              >
                <div class="flex items-center justify-between">
                  <h3 class="text-xl font-semibold text-gray-900">{{ t('faq.items.customize.question') }}</h3>
                  <span class="ml-6 flex-shrink-0">
                    <svg 
                      class="w-6 h-6 transform transition-transform duration-200" 
                      :class="openFaq.includes(4) ? 'rotate-180' : ''"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                </div>
              </button>
              <div 
                class="transition-all duration-200 overflow-hidden"
                :class="openFaq.includes(4) ? 'max-h-96 pb-6' : 'max-h-0'"
              >
                <p class="text-lg text-gray-600">{{ t('faq.items.customize.answer') }}</p>
              </div>
            </div>

            <!-- Question 6 -->
            <div class="border-b border-gray-200 animate-slide-up [animation-delay:1400ms]">
              <button 
                class="w-full py-6 text-left focus:outline-none group"
                @click="toggleFaq(5)"
              >
                <div class="flex items-center justify-between">
                  <h3 class="text-xl font-semibold text-gray-900">{{ t('faq.items.languages.question') }}</h3>
                  <span class="ml-6 flex-shrink-0">
                    <svg 
                      class="w-6 h-6 transform transition-transform duration-200" 
                      :class="openFaq.includes(5) ? 'rotate-180' : ''"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                </div>
              </button>
              <div 
                class="transition-all duration-200 overflow-hidden"
                :class="openFaq.includes(5) ? 'max-h-96 pb-6' : 'max-h-0'"
              >
                <p class="text-lg text-gray-600">{{ t('faq.items.languages.answer') }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Cool Bottom CTA Section -->
      <section class="container mx-auto px-4 py-16 md:py-24">
        <div class="relative overflow-hidden rounded-3xl bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 max-w-5xl mx-auto animate-fade-in" ref="ctaSection">
          <!-- 装饰图案 -->
          <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(255,255,255,0.15) 1px, transparent 0);background-size: 24px 24px;"></div>
          </div>
          
          <!-- 光晕效果 -->
          <div class="absolute left-0 top-0 w-2/3 h-full bg-gradient-to-r from-blue-400/30 to-transparent blur-[100px] opacity-100"></div>
          <div class="absolute right-0 bottom-0 w-2/3 h-full bg-gradient-to-l from-purple-400/30 to-transparent blur-[100px] opacity-100"></div>
          
          <!-- 内容区域 -->
          <div class="relative px-8 py-16 md:px-16 md:py-20">
            <div class="flex flex-col items-center text-center max-w-3xl mx-auto">
              <span class="text-blue-200 font-medium mb-3 text-2xl md:text-3xl">{{ t('bottomCta.subtitle') }}</span>
              <h2 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8 leading-tight">
                {{ t('bottomCta.title') }}
              </h2>
              <div class="flex flex-col sm:flex-row gap-4 justify-center pt-4 animate-slide-up [animation-delay:600ms]">
                <a
                  href="https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white border-2 border-white/30 rounded-lg hover:bg-white/10 transition-all duration-200 backdrop-blur-sm group"
                >
                  <span class="flex items-center">
                    {{ t('bottomCta.button') }}
                    <ArrowRight class="ml-2 h-5 w-5 group-hover:translate-x-0.5 transition-transform" />
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ArrowRight, ArrowDown, Mail, Bug, ClipboardList, Settings } from 'lucide-vue-next'
import { useIntersectionObserver } from '@vueuse/core'
import FeatureSection from '~/components/features/FeatureSection.vue'
import { useSeo } from '~/composables/useSeo'

const { t } = useI18n()
const { setSeoMeta } = useSeo()

// Set SEO meta tags for home page
setSeoMeta()

// 使用翻译键数组
const wordKeys = [
  'hero.title.rotatingWords.0',
  'hero.title.rotatingWords.1',
  'hero.title.rotatingWords.2',
  'hero.title.rotatingWords.3',
  'hero.title.rotatingWords.4'
]
const currentWord = ref(t(wordKeys[0]))
let wordIndex = 0
let intervalId: NodeJS.Timeout

const ctaSection = ref<HTMLElement | null>(null)
const featuresSection = ref<HTMLElement | null>(null)
const statsSection = ref<HTMLElement | null>(null)

// Stats animation
const formCount = ref(0)
const accuracyRate = ref(0)
let animationStarted = false

const animateValue = (start: number, end: number, duration: number, setValue: (value: number) => void) => {
  const startTime = performance.now()
  
  const updateValue = (currentTime: number) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // Easing function for smooth animation
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    const currentValue = Math.floor(start + (end - start) * easeOutQuart)
    
    setValue(currentValue)
    
    if (progress < 1) {
      requestAnimationFrame(updateValue)
    }
  }
  
  requestAnimationFrame(updateValue)
}

const startStatsAnimation = () => {
  if (!animationStarted) {
    animationStarted = true
    animateValue(0, 500, 2000, (value) => formCount.value = value)
    animateValue(0, 98, 2000, (value) => accuracyRate.value = value)
  }
}

const scrollToFeatures = () => {
  featuresSection.value?.scrollIntoView({ behavior: 'smooth' })
}

const rotateWords = () => {
  wordIndex = (wordIndex + 1) % wordKeys.length
  currentWord.value = t(wordKeys[wordIndex])
}

const openFaq = ref<number[]>([])
const toggleFaq = (index: number) => {
  const position = openFaq.value.indexOf(index)
  if (position > -1) {
    openFaq.value.splice(position, 1)
  } else {
    openFaq.value.push(index)
  }
}

// FAQ animation observer
const faqSection = ref<HTMLElement | null>(null)

onMounted(() => {
  // 默认展开第一个 FAQ
  openFaq.value = [0]
  
  intervalId = setInterval(rotateWords, 3000)

  // Stats animation observer
  useIntersectionObserver(statsSection, ([{ isIntersecting }]) => {
    if (isIntersecting) {
      startStatsAnimation()
    }
  })

  // FAQ Section observer
  useIntersectionObserver(faqSection, ([{ isIntersecting }]) => {
    if (isIntersecting && faqSection.value) {
      faqSection.value.classList.add('animate-feature')
    }
  })
})

onUnmounted(() => {
  clearInterval(intervalId)
})
</script>

<style scoped>
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out forwards;
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
  will-change: transform, opacity;
}
</style>

