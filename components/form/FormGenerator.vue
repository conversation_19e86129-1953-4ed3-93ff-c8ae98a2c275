<script setup lang="ts">
import { ref } from 'vue'
import type { FormRequest } from '~/types/form.types'

const props = defineProps<{
  formData: FormRequest
}>()

const emit = defineEmits<{
  (e: 'update', value: any): void
  (e: 'error', error: Error): void
}>()

const isGenerating = ref(false)
const retryCount = ref(0)
const MAX_RETRIES = 2
const RETRY_DELAY = 2000 // 2 seconds delay

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

const generateForm = async () => {
  if (retryCount.value >= MAX_RETRIES) {
    emit('error', new Error('Maximum retry attempts reached'))
    return
  }

  isGenerating.value = true
  
  try {
    const response = await fetch('/api/form/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(props.formData)
    })

    const result = await response.json()

    if (result.success) {
      emit('update', result.data)
      retryCount.value = 0 // Reset retry counter
    } else {
      throw new Error(result.error?.message || 'Generation failed')
    }
  } catch (error) {
    console.error('Error:', error)
    retryCount.value++
    
    if (error instanceof Error) {
      const shouldRetry = (
        error.message.includes('timeout') || 
        error.message.includes('429') || // Rate limit
        error.message.includes('500') || // Server error
        error.message.includes('503') || // Service unavailable
        error.message.includes('504')    // Gateway timeout
      ) && retryCount.value < MAX_RETRIES

      if (shouldRetry) {
        console.log(`Waiting ${RETRY_DELAY/1000}s before retry (${retryCount.value}/${MAX_RETRIES})...`)
        await sleep(RETRY_DELAY)
        return generateForm()
      } else {
        emit('error', error)
      }
    }
  } finally {
    isGenerating.value = false
  }
}

// Export method for parent component
defineExpose({
  generateForm
})
</script>

<template>
  <div class="w-full">
    <div v-if="isGenerating" class="p-4 rounded-lg bg-gray-50 border border-gray-200">
      <div class="animate-pulse">
        Generating form content...
        <span v-if="retryCount > 0" class="text-yellow-600">
          (Attempt {{ retryCount }}/{{ MAX_RETRIES }})
        </span>
      </div>
    </div>
  </div>
</template>
</script> 