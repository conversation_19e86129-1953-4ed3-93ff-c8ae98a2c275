<template>
  <div class="relative inline-block language-switcher">
    <!-- 触发按钮 -->
    <button
      @click.stop="isOpen = !isOpen"
      class="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors p-2"
      :aria-label="t('nav.language')"
      :aria-expanded="isOpen"
    >
      <Globe class="w-5 h-5" />
    </button>

    <!-- 下拉菜单 -->
    <Transition
      enter-active-class="transition duration-100 ease-out"
      enter-from-class="transform scale-95 opacity-0"
      enter-to-class="transform scale-100 opacity-100"
      leave-active-class="transition duration-75 ease-in"
      leave-from-class="transform scale-100 opacity-100"
      leave-to-class="transform scale-95 opacity-0"
    >
      <div
        v-if="isOpen"
        @click.stop
        class="absolute z-[1000] w-[180px]"
        style="left: 50%; transform: translateX(-50%); top: 100%; margin-top: 8px;"
      >
        <!-- 菜单主体 -->
        <div class="relative bg-white rounded-lg shadow-lg">
          <!-- 三角标 -->
          <div class="absolute -top-2 left-1/2 -translate-x-1/2 w-4 h-4">
            <div class="absolute w-4 h-4 bg-white rotate-45 border border-gray-200"></div>
          </div>
          
          <!-- 菜单内容 -->
          <div class="relative bg-white rounded-lg overflow-hidden">
            <div class="py-1 max-h-[400px] overflow-y-auto custom-scrollbar" role="menu" aria-orientation="vertical">
              <!-- 语言选项 -->
              <button
                v-for="lang in availableLocales"
                :key="lang.code"
                @click="switchLanguage(lang.code)"
                class="w-full flex items-center justify-between px-4 py-2.5 text-sm hover:bg-gray-50 transition-colors"
                :class="{ 'bg-gray-50': currentLocale === lang.code }"
                role="menuitem"
              >
                <span>{{ lang.nativeName }}</span>
                <Check v-if="currentLocale === lang.code" class="w-4 h-4 text-blue-600" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Globe, Check } from 'lucide-vue-next'

const { t } = useI18n()
const { availableLocales, currentLocale, switchLanguage } = useLanguages()
const isOpen = ref(false)

// 关闭菜单的处理函数
const closeMenu = () => {
  isOpen.value = false
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.language-switcher')) {
    closeMenu()
  }
}

// 滚动时关闭菜单
const handleScroll = () => {
  closeMenu()
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeMenu()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('scroll', handleScroll, true)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('scroll', handleScroll, true)
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E0 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #CBD5E0;
  border-radius: 2px;
}

.rotate-45 {
  border-left: 1px solid #e5e7eb;
  border-top: 1px solid #e5e7eb;
  border-right: none;
  border-bottom: none;
}
</style>