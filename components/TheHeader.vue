<template>
  <header class="fixed top-0 left-0 right-0 h-[60px] z-50 transition-all duration-200"
    :class="[
      isMenuOpen
        ? 'bg-white'
        : (isScrolled || isDashboard)
          ? 'bg-white/80 backdrop-blur-md border-b border-gray-100' 
          : 'bg-transparent'
    ]"
  >
    <div class="container mx-auto px-4 h-full">
      <nav class="flex items-center justify-between h-full">
        <!-- Logo -->
        <NuxtLink :to="localePath('/')" class="flex items-center gap-3">
          <img src="/logo/Fillify-Logo.svg" alt="Fillify Logo" class="h-8 w-auto" />
          <span class="text-xl font-semibold text-gray-900">
            Fillify
          </span>
        </NuxtLink>

        <!-- 桌面端右侧项目组 -->
        <div v-if="!isSignInPage" class="hidden lg:flex items-center gap-6">
          <!-- 博客链接 -->
          <NuxtLink
            :to="localePath('/blog')"
            class="text-gray-600 hover:text-gray-900 transition-colors"
          >
            {{ t('nav.blog') }}
          </NuxtLink>

          <!-- 桌面端语言切换器 -->
          <LanguageSwitcher />

          <!-- 登录按钮组 -->
          <div class="flex items-center gap-3">
            <template v-if="!isLoggedIn">
              <NuxtLink
                :to="localePath('/signin')"
                class="text-gray-600 hover:text-gray-900"
              >
                {{ t('nav.signin') }}
              </NuxtLink>
              <NuxtLink
                href="https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn"
                target="_blank"
                rel="noopener noreferrer"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                {{ t('nav.startFree') }}
              </NuxtLink>
            </template>
            <template v-else>
              <div class="relative group">
                <div 
                  @click="handleDashboardNavigation"
                  class="w-8 h-8 rounded-full overflow-hidden bg-blue-600 flex items-center justify-center text-white cursor-pointer hover:ring-2 hover:ring-blue-600 hover:ring-offset-2 transition duration-200"
                >
                  <img v-if="user?.picture_url" :src="user.picture_url" :alt="getDisplayName(user)" class="w-full h-full object-cover">
                  <span v-else class="text-sm font-medium">{{ user ? getDisplayName(user)[0] : 'U' }}</span>
                </div>
                
                <div class="invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-200 absolute -right-2 min-w-[180px] rounded-xl bg-white p-2 shadow-lg border border-gray-100 mt-4">
                  <div class="absolute -top-2 right-4 w-4 h-4 rotate-45 bg-white border-l border-t border-gray-100"></div>
                  <div class="relative">
                    <button 
                      @click="handleDashboardNavigation" 
                      class="w-full relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2.5 text-sm outline-none transition-colors hover:bg-gray-100 focus:bg-gray-100"
                    >
                      <LayoutDashboard class="w-4 h-4 mr-2 opacity-70" />
                      <span>{{ t('nav.dashboard') }}</span>
                    </button>
                    <div class="my-1 h-px bg-gray-200" />
                    <button 
                      @click="signOut" 
                      class="w-full relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2.5 text-sm outline-none transition-colors hover:bg-red-50 focus:bg-red-50 text-red-600"
                    >
                      <LogOut class="w-4 h-4 mr-2 opacity-70" />
                      <span>{{ t('nav.signout') }}</span>
                    </button>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 移动端菜单按钮 -->
        <button 
          v-if="!isSignInPage"
          @click="isMenuOpen = !isMenuOpen"
          class="lg:hidden p-2 text-gray-600 hover:text-gray-900"
          aria-label="Toggle menu"
        >
          <Menu v-if="!isMenuOpen" class="w-5 h-5" />
          <X v-else class="w-5 h-5" />
        </button>
      </nav>
    </div>

    <!-- 移动端菜单 -->
    <Transition
      enter-active-class="transition duration-200 ease-out"
      enter-from-class="opacity-0 -translate-y-1"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition duration-150 ease-in"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 -translate-y-1"
    >
      <div 
        v-if="isMenuOpen && !isSignInPage" 
        class="lg:hidden absolute top-[60px] left-0 right-0 bg-white shadow-lg py-4"
      >
        <div class="container mx-auto px-4">
          <div class="flex flex-col gap-4">
            <!-- 移动端博客链接 -->
            <NuxtLink
              :to="localePath('/blog')"
              class="flex items-center gap-2 text-gray-600 hover:text-gray-900 py-2"
              @click="isMenuOpen = false"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-newspaper w-5 h-5">
                <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"/>
                <path d="M18 14h-8"/>
                <path d="M15 18h-5"/>
                <path d="M10 6h8v4h-8V6Z"/>
              </svg>
              <span>{{ t('nav.blog') }}</span>
            </NuxtLink>

            <!-- 分隔线 -->
            <div class="h-px bg-gray-100" />

            <!-- 移动端语言切换器 -->
            <div>
              <div class="flex items-center gap-2 text-gray-600 mb-2">
                <Globe class="w-5 h-5" />
                <span>{{ t('nav.language') }}</span>
              </div>
              <div class="pl-7">
                <button
                  v-for="lang in availableLocales"
                  :key="lang.code"
                  @click="handleLanguageSwitch(lang.code)"
                  class="block w-full text-left py-2 text-sm text-gray-600 hover:text-gray-900"
                  :class="{ 'text-blue-600 font-medium': currentLocale === lang.code }"
                >
                  {{ lang.nativeName }}
                </button>
              </div>
            </div>

            <!-- 分隔线 -->
            <div class="h-px bg-gray-100" />

            <!-- 登录选项 -->
            <template v-if="!isLoggedIn">
              <NuxtLink
                :to="localePath('/signin')"
                class="flex items-center gap-2 text-gray-600 hover:text-gray-900 py-2"
                @click="isMenuOpen = false"
              >
                <LogIn class="w-5 h-5" />
                <span>{{ t('nav.signin') }}</span>
              </NuxtLink>
              <NuxtLink
                href="https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn"
                target="_blank"
                rel="noopener noreferrer"
                class="flex items-center gap-2 text-blue-600 hover:text-blue-700 py-2"
                @click="isMenuOpen = false"
              >
                <Chrome class="w-5 h-5" />
                <span>{{ t('nav.startFree') }}</span>
              </NuxtLink>
            </template>
            <template v-else>
              <button
                @click="handleMobileNavigation"
                class="flex items-center gap-2 text-gray-600 hover:text-gray-900 py-2 w-full text-left"
              >
                <LayoutDashboard class="w-5 h-5" />
                <span>{{ t('nav.dashboard') }}</span>
              </button>
              <button
                @click="signOutAndClose"
                class="flex items-center gap-2 text-red-600 hover:text-red-700 py-2 w-full text-left"
              >
                <LogOut class="w-5 h-5" />
                <span>{{ t('nav.signout') }}</span>
              </button>
            </template>
          </div>
        </div>
      </div>
    </Transition>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { Menu, X, Globe, LayoutDashboard, LogOut, LogIn, Chrome } from 'lucide-vue-next'

interface User {
  id: string
  email: string
  full_name: string
  picture_url?: string
  credits?: number
}

const isScrolled = ref(false)
const isMenuOpen = ref(false)
const { t, locale } = useI18n()
const { user, isLoggedIn, signOut, fetchUserData, getDisplayName } = useAuth()
const { availableLocales, currentLocale, switchLanguage } = useLanguages()
const route = useRoute()
const localePath = useLocalePath()

// 检查是否在登录页面
const isSignInPage = computed(() => route.path.includes('/signin'))
// 检查是否在 dashboard 页面
const isDashboard = computed(() => {
  // 匹配 /dashboard 或 /{locale}/dashboard 格式的路径
  return /^\/([a-z]{2}(-[A-Z]{2})?\/)?dashboard\/?$/.test(route.path)
})

// 处理滚动效果
const handleScroll = () => {
  isScrolled.value = window.scrollY > 0
}

// 切换语言并关闭菜单
const handleLanguageSwitch = (lang: string) => {
  switchLanguage(lang)
  isMenuOpen.value = false
}

// 登出并关闭菜单
const signOutAndClose = () => {
  signOut()
  isMenuOpen.value = false
}

// 处理 dashboard 导航
const handleDashboardNavigation = async () => {
  try {
    const currentLang = locale.value
    const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
    await navigateTo(dashboardPath, { replace: true })
  } catch (navError) {
    console.error('Navigation error:', navError)
    // 如果导航失败，使用 window.location
    window.location.href = localePath('/dashboard')
  }
}

// 处理移动端导航
const handleMobileNavigation = async () => {
  isMenuOpen.value = false
  await handleDashboardNavigation()
}

onMounted(() => {
  fetchUserData()
  
  // 添加滚动监听
  window.addEventListener('scroll', handleScroll)
  handleScroll() // 初始检查

  // 监听窗口大小变化，在大屏幕时自动关闭移动菜单
  window.addEventListener('resize', () => {
    if (window.innerWidth >= 1024) { // lg breakpoint
      isMenuOpen.value = false
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.animate-fade-in {
  opacity: 0;
  animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>