<template>
  <div class="feature-section" ref="featureSectionRef">
    <div class="container mx-auto px-4">
      <div class="glass-effect rounded-3xl">
        <div class="flex flex-col md:flex-row items-center gap-8 p-8 md:p-12" :class="{ 'md:flex-row-reverse': reverse }">
          <!-- Image Section -->
          <div class="w-full md:w-1/2">
            <div class="aspect-[4/3] relative">
              <img 
                :src="image" 
                :alt="t(`features.${translationKey}.alt`)"
                class="w-full h-full object-contain absolute inset-0"
                loading="lazy"
              />
            </div>
          </div>
          
          <!-- Content Section -->
          <div class="w-full md:w-1/2 space-y-6">
            <div class="bg-blue-100 p-3 rounded-lg w-fit">
              <component :is="icon" class="h-6 w-6 text-blue-600" />
            </div>
            <h3 class="text-2xl md:text-3xl font-bold">
              {{ title }}
            </h3>
            <p class="text-lg text-gray-600 leading-relaxed">
              {{ description }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'
import { ref } from 'vue'
import { useIntersectionObserver } from '@vueuse/core'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface Props {
  icon: Component
  title: string
  description: string
  image: string
  reverse?: boolean
  translationKey: string
}

withDefaults(defineProps<Props>(), {
  reverse: false
})

const featureSectionRef = ref<HTMLElement | null>(null)

// 设置交叉观察器
useIntersectionObserver(featureSectionRef, ([{ isIntersecting }]) => {
  if (isIntersecting && featureSectionRef.value) {
    featureSectionRef.value.classList.add('animate-feature')
  }
})
</script>

<style scoped>
.feature-section {
  position: relative;
  opacity: 0;
  transform: translateY(20px);
  margin: 2rem 0;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
}

.feature-section.animate-feature {
  animation: feature-fade-in 0.8s ease-out forwards;
}

@keyframes feature-fade-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 