<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const socialLinks = [
  {
    name: t('footer.social.twitter'),
    href: 'https://x.com/HiFillify',
    icon: `<svg viewBox="0 0 24 24" class="h-5 w-5 fill-current" xmlns="http://www.w3.org/2000/svg">
      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
    </svg>`
  },
  {
    name: t('footer.social.youtube'),
    href: 'https://www.youtube.com/@HiFillify',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 fill-current" viewBox="0 0 24 24">
      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
    </svg>`
  }
]

const currentYear = new Date().getFullYear()
</script>

<template>
  <footer class="container mx-auto px-4 py-8 border-t animate-fade-in">
    <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
      <div class="text-sm text-gray-600">
        {{ t('footer.copyright', { year: currentYear }) }}
      </div>
      <div class="flex items-center space-x-6">
        <div class="flex space-x-4">
          <a
            v-for="item in socialLinks"
            :key="item.name"
            :href="item.href"
            :title="item.name"
            class="text-gray-400 hover:text-gray-900 transition-colors duration-200"
            target="_blank"
            rel="noopener noreferrer"
            v-html="item.icon"
          />
        </div>
        <div class="h-4 w-px bg-gray-200 mx-2" />
        <NuxtLink :to="localePath('/terms')" class="text-sm text-gray-600 hover:text-gray-900">
          {{ t('footer.links.terms') }}
        </NuxtLink>
        <NuxtLink :to="localePath('/privacy')" class="text-sm text-gray-600 hover:text-gray-900">
          {{ t('footer.links.privacy') }}
        </NuxtLink>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.animate-fade-in {
  opacity: 0;
  animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>