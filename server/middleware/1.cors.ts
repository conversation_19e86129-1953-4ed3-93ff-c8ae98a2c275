import { defineEventHandler, getRequestHeaders, setResponseHeaders } from 'h3'

export default defineEventHandler((event) => {
  // 只处理 API 请求
  if (!event.path.startsWith('/api')) {
    return
  }

  // 获取请求头中的 Origin
  const origin = getRequestHeaders(event).origin || '*'

  // 设置 CORS 头
  setResponseHeaders(event, {
    'Access-Control-Allow-Origin': origin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400',
  })

  // 处理预检请求
  if (event.method === 'OPTIONS') {
    setResponseHeaders(event, {
      'Content-Length': '0',
      'Content-Type': 'text/plain',
    })
    event.node.res.statusCode = 204
    event.node.res.statusMessage = 'No Content'
    return 'OK'
  }
}) 