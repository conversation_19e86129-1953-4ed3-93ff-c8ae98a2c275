import { defineE<PERSON><PERSON><PERSON><PERSON>, readBody, createError } from 'h3'
import { useSupabaseServer } from '~/composables/useSupabase'

export default defineEventHandler(async (event) => {
  try {
    const { email } = await readBody(event)

    if (!email) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Email is required'
      })
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid email format'
      })
    }

    const config = useRuntimeConfig()

    // 检查是否使用虚拟数据（开发环境）
    const isUsingMockData = config.supabaseUrl.includes('demo-project-id')

    if (isUsingMockData) {
      // 模拟发送验证码
      console.log(`[MOCK] Sending OTP to ${email}`)
      console.log(`[MOCK] Verification code: 123456`)

      return {
        success: true,
        message: 'Verification code sent successfully (MOCK)',
        mock: true
      }
    }

    const supabase = useSupabaseServer()

    // 使用 Supabase Auth 发送 OTP
    const { data, error } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        shouldCreateUser: true, // 如果用户不存在则创建
        emailRedirectTo: undefined, // 不需要重定向URL，因为我们使用验证码
      }
    })

    if (error) {
      console.error('Supabase OTP error:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to send verification code'
      })
    }

    return {
      success: true,
      message: 'Verification code sent successfully'
    }

  } catch (error: any) {
    console.error('Send OTP error:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
