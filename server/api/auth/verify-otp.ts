import { defineEventHand<PERSON>, readBody, createError } from 'h3'
import { useSupabaseServer } from '~/composables/useSupabase'

export default defineEventHandler(async (event) => {
  try {
    const { email, token } = await readBody(event)

    if (!email || !token) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Email and verification code are required'
      })
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid email format'
      })
    }

    const config = useRuntimeConfig()

    // 检查是否使用虚拟数据（开发环境）
    const isUsingMockData = config.supabaseUrl.includes('demo-project-id')

    if (isUsingMockData) {
      // 模拟验证码验证
      console.log(`[MOCK] Verifying OTP for ${email}: ${token}`)

      // 只接受 123456 作为有效验证码
      if (token !== '123456') {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid verification code. Use 123456 for testing.'
        })
      }

      // 创建模拟用户
      const mockUser = {
        id: `mock-user-${Date.now()}`,
        email: email,
        full_name: email.split('@')[0],
        credits: 3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log(`[MOCK] User authenticated:`, mockUser)

      return {
        success: true,
        user: mockUser,
        session: {
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token'
        },
        mock: true
      }
    }

    const supabase = useSupabaseServer()

    // 使用 Supabase Auth 验证 OTP
    const { data, error } = await supabase.auth.verifyOtp({
      email: email,
      token: token,
      type: 'email'
    })

    if (error) {
      console.error('Supabase OTP verification error:', error)
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid or expired verification code'
      })
    }

    if (!data.user) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Verification failed'
      })
    }

    // 检查用户是否已存在于我们的用户表中
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', data.user.id)
      .single()

    let user = existingUser

    // 如果用户不存在，创建新用户记录
    if (!existingUser) {
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          id: data.user.id,
          email: data.user.email!,
          full_name: data.user.user_metadata?.full_name || data.user.email!.split('@')[0],
          credits: 3 // 新用户默认3个积分
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating user:', createError)
        throw createError({
          statusCode: 500,
          statusMessage: 'Failed to create user account'
        })
      }

      user = newUser
    }

    return {
      success: true,
      user: user,
      session: data.session
    }

  } catch (error: any) {
    console.error('Verify OTP error:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
