{"permissions": {"allow": ["Bash(npm run dev)", "<PERSON><PERSON>(curl -s http://localhost:3000/blog)", "<PERSON><PERSON>(curl -s \"http://localhost:3000/blog/introducing-fillify-ai-powered-form-automation\")", "<PERSON><PERSON>(curl -s \"http://localhost:3000/zh/blog\")", "WebSearch", "WebFetch(domain:content.nuxt.com)", "WebFetch(domain:masteringnuxt.com)", "Bash(npm install @nuxt/content)", "<PERSON><PERSON>(mkdir -p content/blog)", "Bash(rm -rf /Users/<USER>/Downloads/Github/fillify-web-prod/components/blog /Users/<USER>/Downloads/Github/fillify-web-prod/composables/useBlog.ts /Users/<USER>/Downloads/Github/fillify-web-prod/types/blog.types.ts)", "Bash(npm install better-sqlite3)", "Bash(rm /Users/<USER>/Downloads/Github/fillify-web-prod/content.config.ts)", "Bash(npm uninstall @nuxt/content)", "Bash(npm install @nuxt/content@^2.13.2)", "Bash(curl -s http://localhost:3000/api/_content/query)", "Bash(curl -s http://localhost:3000/api/_content)", "WebFetch(domain:v2.content.nuxt.com)", "Bash(curl -s http://localhost:3002/api/_content/query)", "Bash(curl -s http://localhost:3002/api/_content)", "<PERSON><PERSON>(curl -s http://localhost:3002/blog)", "<PERSON><PERSON>(curl -s \"http://localhost:3000/blog\")", "<PERSON><PERSON>(curl -s \"http://localhost:3000/test-content\")", "Bash(rm /Users/<USER>/Downloads/Github/fillify-web-prod/pages/test-content.vue)", "<PERSON><PERSON>(curl -s http://localhost:3000/signin)", "<PERSON><PERSON>(curl -s http://localhost:3000/zh/signin)", "<PERSON><PERSON>(curl -s http://localhost:3001/signin)", "<PERSON><PERSON>(curl -s \"http://localhost:3001/signin\")", "Bash(curl -s http://localhost:3001/test-supabase)", "Bash(curl -v \"https://jdqhpegkyqqmutcvijph.supabase.co/auth/v1/signup\" -H \"apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpkcWhwZWdreXFxbXV0Y3ZpanBoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM2Njg2NDcsImV4cCI6MjA0OTI0NDY0N30.pUQ24p-7I89S7ec07r-dkA01D_fVyo06J2Gn9iGbvIk\")", "Bash(ping -c 3 jdqhpegkyqqmutcvijph.supabase.co)", "<PERSON><PERSON>(curl -X POST \"https://jdqhpegkyqqmutcvijph.supabase.co/auth/v1/otp\" )", "Bash(rm /Users/<USER>/Downloads/Github/fillify-web-prod/pages/test-supabase.vue /Users/<USER>/Downloads/Github/fillify-web-prod/pages/quick-test.vue /Users/<USER>/Downloads/Github/fillify-web-prod/pages/deep-test.vue /Users/<USER>/Downloads/Github/fillify-web-prod/supabase-fix.sql)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(curl -s \"http://localhost:3000/signin\")", "Bash(curl -s -X POST \"http://localhost:3000/api/auth/send-otp\" -H \"Content-Type: application/json\" -d '{\"\"\"\"email\"\"\"\":\"\"\"\"<EMAIL>\"\"\"\"}')", "Bash(curl -s -X POST \"http://localhost:3000/api/auth/send-otp\" -H \"Content-Type: application/json\" -d '{\"\"email\"\":\"\"<EMAIL>\"\"}')", "Bash(curl -s -X POST \"http://localhost:3000/api/auth/verify-otp\" -H \"Content-Type: application/json\" -d '{\"\"email\"\":\"\"<EMAIL>\"\",\"\"token\"\":\"\"123456\"\"}')", "Bash(echo $SUPABASE_URL)", "<PERSON><PERSON>(curl -s \"http://localhost:3000/dashboard\")", "Bash(curl -s -X POST \"http://localhost:3001/api/users/get-user\" -H \"Content-Type: application/json\" -d '{\"\"userId\"\":\"\"test-user-id\"\"}')", "Bash(curl -v \"http://localhost:3001/api/users/get-user\" -X POST -H \"Content-Type: application/json\" -d '{\"\"userId\"\":\"\"test\"\"}')", "Bash(curl -v \"http://localhost:8080/api/users/get-user\" -X POST -H \"Content-Type: application/json\" -d '{\"\"userId\"\":\"\"test\"\"}')", "Bash(lsof -i :8080)", "Bash(lsof -i :3001)", "Bash(lsof -i :3000)", "<PERSON><PERSON>(curl -s \"http://localhost:8080/\")", "<PERSON><PERSON>(curl -s \"http://localhost:8080/health\")", "<PERSON><PERSON>(curl -s \"http://localhost:8080/api/health\")", "<PERSON><PERSON>(curl -s \"http://localhost:8080/status\")", "Bash(curl -s -X POST \"http://localhost:8080/api/users/get-user\" -H \"Content-Type: application/json\" -d '{\"\"userId\"\":\"\"test-user-id\"\"}')", "<PERSON>sh(git log --oneline -5)", "Bash(git add .)", "Bash(git commit -m \"$(cat <<''EOF''\nfix: enhance user display logic and resolve backend authentication issues\n\n- Add getDisplayName utility function to show email prefix when full_name is empty\n- Update avatar display logic in both header and dashboard to use first letter of display name\n- Fix authentication flow to use correct API base URL (port 8080)\n- Implement fallback mechanism for user data when backend is unavailable\n- Improve global state management with proper user state synchronization\n- Enhance error handling in fetchUserData with local storage fallback\n\n🤖 Generated with [Claude Code](https://claude.ai/code)\n\nCo-Authored-By: <PERSON> <<EMAIL>>\nEOF\n)\")", "mcp__supabase__list_projects", "mcp__supabase__list_tables", "mcp__supabase__execute_sql"], "deny": [], "ask": []}}