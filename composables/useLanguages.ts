import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

export const useLanguages = () => {
  const { locale } = useI18n()
  const router = useRouter()
  
  // 所有支持的语言列表
  const availableLocales = [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'es', name: 'Spanish', nativeName: 'Español' },
    { code: 'fr', name: 'French', nativeName: 'Français' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語' },
    { code: 'ko', name: 'Korean', nativeName: '한국어' },
    { code: 'zh', name: 'Chinese', nativeName: '简体中文' },
    { code: 'zh-TW', name: 'Traditional Chinese', nativeName: '繁體中文' },
    { code: 'ru', name: 'Russian', nativeName: 'Русский' }
  ]

  // 当前语言
  const currentLocale = computed(() => locale.value)

  // 切换语言
  const switchLanguage = (lang: string) => {
    locale.value = lang
    // 保存语言偏好到 cookie
    document.cookie = `fillify-lang=${lang}; path=/; max-age=31536000`
    
    // 更新 URL 路径
    const currentPath = window.location.pathname
    const pathParts = currentPath.split('/').filter(Boolean) // 移除空字符串
    const firstPart = pathParts[0]
    const isValidLang = ['en', 'zh', 'zh-TW', 'es', 'fr', 'ko', 'ja', 'ru'].includes(firstPart)
    
    // 构建新的 URL 路径
    let newPath
    if (isValidLang) {
      // 如果当前路径包含语言前缀，移除第一个部分（语言代码）
      const remainingPath = pathParts.slice(1).join('/')
      if (lang === 'en') {
        // 英文版本：不添加语言前缀
        newPath = remainingPath ? `/${remainingPath}` : '/'
      } else {
        // 其他语言：替换语言前缀
        newPath = remainingPath ? `/${lang}/${remainingPath}` : `/${lang}`
      }
    } else {
      // 如果当前路径没有语言前缀
      if (lang === 'en') {
        // 英文版本：直接使用当前路径
        newPath = currentPath
      } else {
        // 其他语言：在路径前添加语言前缀
        const cleanPath = currentPath === '/' ? '' : currentPath
        newPath = `/${lang}${cleanPath}`
      }
    }
    
    // 使用 router.push 进行导航
    router.push(newPath)
  }

  return {
    availableLocales,
    currentLocale,
    switchLanguage
  }
}