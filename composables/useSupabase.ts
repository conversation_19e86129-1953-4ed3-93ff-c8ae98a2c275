import { createClient } from '@supabase/supabase-js'

// 定义用户类型
export interface User {
  id: string
  email: string
  full_name?: string
  picture_url?: string
  credits?: number
  created_at?: string
  updated_at?: string
}

// 定义数据库类型
export interface Database {
  public: {
    Tables: {
      users: {
        Row: User
        Insert: Omit<User, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<User, 'id' | 'created_at' | 'updated_at'>>
      }
    }
  }
}

let supabaseClient: ReturnType<typeof createClient<Database>> | null = null

export const useSupabase = () => {
  const config = useRuntimeConfig()

  // 只在客户端创建客户端实例
  if (process.client && !supabaseClient) {
    const supabaseUrl = config.public.supabaseUrl
    const supabaseAnonKey = config.public.supabaseAnonKey

    if (supabaseUrl && supabaseAnonKey) {
      supabaseClient = createClient<Database>(
        supabaseUrl,
        supabase<PERSON><PERSON>Key,
        {
          auth: {
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: true
          }
        }
      )
    }
  }

  return supabaseClient
}

// 服务端使用的 Supabase 客户端（使用 service role key）
export const useSupabaseServer = () => {
  const config = useRuntimeConfig()
  
  return createClient<Database>(
    config.supabaseUrl,
    config.supabaseServiceRoleKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}
