import { ref } from 'vue'

// 添加 chrome 类型声明
declare global {
  interface Window {
    chrome?: {
      runtime?: {
        sendMessage?: (extensionId: string, message: any, callback: (response: any) => void) => void
        lastError?: any
      }
    }
  }
}

export const useExtension = () => {
  const isExtensionInstalled = ref(false)
  const config = useRuntimeConfig()
  const extensionId = config.public.EXTENSION_ID

  const checkExtensionInstalled = () => {
    if (typeof window === 'undefined' || !window.chrome?.runtime?.sendMessage) {
      return Promise.resolve(false)
    }
    
    return new Promise((resolve) => {
      try {
        // 使用类型断言来解决 TypeScript 的类型检查问题
        const sendMessage = (window.chrome?.runtime?.sendMessage as any)
        sendMessage(extensionId, { type: 'CHECK_INSTALLATION' }, 
          (response: any) => {
            const isInstalled = !!response && !window.chrome?.runtime?.lastError
            isExtensionInstalled.value = isInstalled
            resolve(isInstalled)
          }
        )
        
        setTimeout(() => {
          if (!isExtensionInstalled.value) {
            resolve(false)
          }
        }, 1000)
      } catch (error) {
        console.error('Extension check error:', error)
        isExtensionInstalled.value = false
        resolve(false)
      }
    })
  }

  return {
    isExtensionInstalled,
    checkExtensionInstalled
  }
} 