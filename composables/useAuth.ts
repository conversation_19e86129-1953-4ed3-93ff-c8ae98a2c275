import { useState } from 'nuxt/app'
import { computed } from 'vue'
import { cookieUtil } from '~/utils/cookie'

// 定义用户类型
type User = {
  id: string
  email: string
  full_name: string
  picture_url?: string
  credits?: number
}

// 获取用户显示名称的工具函数
const getDisplayName = (user: User): string => {
  if (user.full_name && user.full_name.trim() && user.full_name !== 'User') {
    return user.full_name
  }
  if (user.email) {
    return user.email.split('@')[0]
  }
  return 'User'
}

// 定义可能的域名配置
const DOMAINS: (string | undefined)[] = [
  undefined, // 默认域名
  'localhost',
  'fillify.tech',
  '.fillify.tech'
]

export const useAuth = () => {
  const config = useRuntimeConfig()
  const apiBase = config.public.apiBase
  const user = useState<User | null>('user', () => null)
  const isLoggedIn = computed(() => !!user.value)

  // 更新全局状态的辅助函数
  const updateGlobalState = (userData: User) => {
    if (process.client) {
      // 更新本地存储
      const userState = {
        isLoggedIn: true,
        credits: userData.credits || 3,
        userId: userData.id,
        email: userData.email,
        fullName: userData.full_name
      }
      localStorage.setItem('userState', JSON.stringify(userState))
      
      // 触发全局状态更新事件
      window.dispatchEvent(new CustomEvent('userStateUpdate', { detail: userState }))
    }
  }

  // 发送邮箱验证码
  const sendEmailOTP = async (email: string) => {
    try {
      const response = await $fetch('/api/auth/send-otp', {
        method: 'POST',
        body: { email }
      })
      return response
    } catch (error) {
      console.error('Send OTP error:', error)
      throw error
    }
  }

  // 验证邮箱验证码并登录
  const verifyEmailOTP = async (email: string, token: string) => {
    try {
      const response = await $fetch('/api/auth/verify-otp', {
        method: 'POST',
        body: { email, token }
      }) as any

      if (response.success && response.user) {
        user.value = response.user
        updateGlobalState(response.user)
        return response
      }

      throw new Error('Verification failed')
    } catch (error) {
      console.error('Verify OTP error:', error)
      throw error
    }
  }

  // 清理所有认证状态
  const clearAuthState = () => {
    user.value = null
    DOMAINS.forEach(domain => {
      cookieUtil.removeCookie('xToken', {
        path: '/',
        domain,
        secure: window.location.protocol === 'https:',
        sameSite: 'lax'
      })
    })
    localStorage.removeItem('userState')
  }

  const signOut = async () => {
    try {
      // 调用登出 API
      const res = await fetch(`${apiBase}/api/auth/logout`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${cookieUtil.getCookie('xToken')}`
        }
      })

      if (!res.ok) {
        throw new Error(`Logout failed with status: ${res.status}`)
      }
      
      // 清理所有状态
      clearAuthState()
      
      // 强制刷新页面以确保状态完全清理
      window.location.href = '/'
    } catch (error) {
      console.error('Logout error:', error)
      // 即使出错也清理本地状态
      clearAuthState()
      window.location.href = '/'
    }
  }

  const fetchUserData = async () => {
    const xToken = cookieUtil.getCookie('xToken')
    if (!xToken) {
      clearAuthState()
      return
    }

    try {
      // 尝试从后端获取用户数据
      const res = await fetch(`${apiBase}/api/users/get-user`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: xToken }),
      })

      if (!res.ok) {
        throw new Error(`Failed to fetch user data: ${res.status}`)
      }

      const data = await res.json()
      if (data.success && data.user) {
        user.value = data.user
        updateGlobalState(data.user)
      } else {
        console.error('Invalid user data received:', data)
        clearAuthState()
      }
    } catch (error) {
      console.error('Error fetching user data from backend:', error)
      console.log('Trying to use local user data from cookies...')
      
      // 如果后端不可用，尝试从localStorage获取用户数据
      if (process.client) {
        const userState = localStorage.getItem('userState')
        if (userState) {
          try {
            const parsedState = JSON.parse(userState)
            if (parsedState.isLoggedIn && parsedState.userId) {
              // 创建基本用户对象
              const localUser = {
                id: parsedState.userId,
                email: parsedState.email || '<EMAIL>',
                full_name: parsedState.fullName || (parsedState.email ? parsedState.email.split('@')[0] : 'User'),
                credits: parsedState.credits || 3
              }
              user.value = localUser
              updateGlobalState(localUser)
              console.log('Using local user data:', localUser)
              return
            }
          } catch (parseError) {
            console.error('Error parsing local user state:', parseError)
          }
        }
      }
      
      clearAuthState()
    }
  }

  return {
    user,
    isLoggedIn,
    signOut,
    fetchUserData,
    clearAuthState,
    sendEmailOTP,
    verifyEmailOTP,
    getDisplayName
  }
} 