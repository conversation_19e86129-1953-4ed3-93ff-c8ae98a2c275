import en from './locales/en'
import zh from './locales/zh'
import zhTW from './locales/zh-TW'
import es from './locales/es'
import fr from './locales/fr'
import ko from './locales/ko'
import ja from './locales/ja'
import ru from './locales/ru'

export default defineI18nConfig(() => {
  let initialLocale = 'en' // 默认语言

  if (process.client) {
    // 1. 首先从路径中获取语言
    const path = window.location.pathname
    const pathLang = path.split('/')[1]
    
    if (['en', 'zh', 'zh-TW', 'es', 'fr', 'ko', 'ja', 'ru'].includes(pathLang)) {
      initialLocale = pathLang
    } else {
      // 2. 如果路径中没有语言，检查 cookie
      const cookies = document.cookie.split(';')
      const langCookie = cookies.find(cookie => cookie.trim().startsWith('fillify-lang='))
      
      if (langCookie) {
        const lang = langCookie.split('=')[1].trim()
        if (['en', 'zh', 'zh-TW', 'es', 'fr', 'ko', 'ja', 'ru'].includes(lang)) {
          initialLocale = lang
          // 如果检测到 cookie 中的语言偏好，且当前路径不包含语言前缀，则仅在非英文时重定向
          if (lang !== 'en' && (!pathLang || !['en', 'zh', 'zh-TW', 'es', 'fr', 'ko', 'ja', 'ru'].includes(pathLang))) {
            window.location.pathname = `/${lang}${path}`
          }
        }
      }
    }
  }

  return {
    legacy: false,
    locale: initialLocale,
    messages: {
      en,
      zh,
      'zh-TW': zhTW,
      es,
      fr,
      ko,
      ja,
      ru
    }
  }
})
