export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig()
  
  useHead({
    script: [
      {
        src: `https://www.googletagmanager.com/gtag/js?id=${config.public.GOOGLE_ANALYTICS_ID}`,
        async: true,
      },
      {
        children: `window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '${config.public.GOOGLE_ANALYTICS_ID}');`,
      },
    ],
  })
}) 